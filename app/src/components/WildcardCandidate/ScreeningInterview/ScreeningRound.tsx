import React, { useCallback, useEffect, useRef, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import Webcam from "react-webcam";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { openDialog, setLoader } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { QuestionList } from "./QuestionsList";
import {
  InterviewAnswer,
  InterviewDetailInterface,
  ScreeningQuestionInterface,
} from "@src/redux/interfaces";
import Image from "next/image";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { useRouter } from "next/router";
import {
  APP_ROUTE,
  EXAM_TERTMINATION_MAX_WARNINGS as MAX_WARNINGS,
} from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { createFFmpeg, fetchFile } from "@ffmpeg/ffmpeg";
import * as tf from "@tensorflow/tfjs";
import * as blazeface from "@tensorflow-models/blazeface";
import { CodingRound } from "./CodingRound";
import WebcamWindow from "./WebcamWindow";
import { InterviewInstructions } from "./InterviewInstruction";
import { SubmissionConfirmation } from "./SubmissionConfirmation";
import { DisqualificationNotice } from "./DisqualificationNotice";

const ffmpeg = createFFmpeg({ log: false });

const videoConstraints = {
  width: 720,
  height: 480,
  facingMode: "user", // Use "environment" for back camera on mobile devices
};

type ScreeningRoundType = {
  interviewId: number;
  interview: InterviewDetailInterface;
};

export const ScreeningRound: React.FC<ScreeningRoundType> = ({
  interviewId,
  interview,
}) => {
  const testMintues = interview.time_duration ?? 10;
  const dispatch = useAppDispatch();
  const [timeRemaining, setTimeRemaining] = useState<{
    percentage: number;
    time: number;
  }>({
    percentage: 100,
    time: testMintues * 60, // minutes in seconds
  });
  const [screenShareStream, setScreenShareStream] =
    useState<MediaStream | null>(null);
  const screenShareRef = useRef<MediaStream | null>(null);
  const eyeDetection = useRef<boolean>(false); // Ref to store eyeDetection
  const timerRef = useRef<any>(null); // Ref to store timer interval ID
  const testStarted = useRef<any>(null); // Ref to store exam started
  const lastWarningTime = useRef<any>(0); // Ref to store last warning message
  const dialogStatesRef = useRef<boolean>(false); // Ref to store current dialogStates
  const eyeDetectionIssueStartTime = useRef<number | null>(null); // Track when eye detection issue started
  const eyeDetectionTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Timeout for 3-second delay
  const pendingEyeDetectionMessage = useRef<string | null>(null); // Store the message for delayed warning
  const [answers, setAnswers] = useState<InterviewAnswer[]>([]); // Array to store answers
  const router = useRouter();
  const webcamRef = useRef<Webcam>(null);
  const isRequestInProgress = useRef<boolean>(false);
  const prevDetectionResult = useRef<string>("");
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevFacePosition = useRef<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null); // State for media stream
  const [isTestStart, startTest] = useState<boolean>(false);
  const [isTestDisqualified, testDisqualified] = useState<boolean>(false);
  const [submitted, setSubmitted] = useState<boolean>(false);
  const [videoChunks, setVideoChunks] = useState<Blob[]>([]); // Store video chunks
  const [updatedChunks, setUpdatedChunks] = useState<Uint8Array[]>([]); // Store video chunks
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [recordingStopped, setRecordingStopped] = useState<boolean>(false);
  const [jobQueue, setJobQueue] = useState<Blob[]>([]);
  const [questions, setQuestions] = useState<ScreeningQuestionInterface[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [attemptQuestions, setAttemptQuestions] = useState<number>(0);
  const [showCodingRound, setShowCodingRound] = useState<boolean>(false);
  const [codingQuestionId, setCodingQuestionId] = useState<number | null>(null);

  // State to manage warnings
  const [model, setModel] = useState<blazeface.BlazeFaceModel | null>(null);
  const [warningMessages, setWarningMessages] = useState<string[]>([]);


// Add these new refs at the top of your component with other refs
const headMovementStartTime = useRef<{ [key: string]: number | null }>({
  left: null,
  right: null,
  up: null,
  down: null,
  away: null
});
const headMovementTimeoutRefs = useRef<{ [key: string]: NodeJS.Timeout | null }>({
  left: null,
  right: null,
  up: null,
  down: null,
  away: null
});

// New refs for eye gaze detection
const eyeGazeStartTime = useRef<{ [key: string]: number | null }>({
  eyes_left: null,
  eyes_right: null,
  eyes_up: null,
  eyes_down: null,
  eyes_closed: null
});
const eyeGazeTimeoutRefs = useRef<{ [key: string]: NodeJS.Timeout | null }>({
  eyes_left: null,
  eyes_right: null,
  eyes_up: null,
  eyes_down: null,
  eyes_closed: null
});

// Additional refs for eye gaze detection
const currentEyeGazeIssue = useRef<string | null>(null);
const eyeGazeDetectionStartTime = useRef<number | null>(null);
const eyeGazeDetectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
const pendingEyeGazeMessage = useRef<string | null>(null);

const lastApiCallTime = useRef<number>(0);
const currentHeadState = useRef<string>('normal');
const currentEyeGazeState = useRef<string>('normal');
const faceDetectionHistory = useRef<Array<{ timestamp: number, hasValidFace: boolean, position?: any, eyeGaze?: string }>>([]);
const baselineFacePosition = useRef<any>(null);
const baselineEyePosition = useRef<any>(null);
const calibrationFrames = useRef<number>(0);
const isCalibrated = useRef<boolean>(false);

// Constants for detection
const API_CALL_COOLDOWN = 2000; // 2 seconds between API calls
const HEAD_MOVEMENT_DELAY = 5000; // 5 seconds delay for head movement
const EYE_GAZE_DELAY = 4000; // 4 seconds delay for eye gaze detection
const CALIBRATION_FRAMES_NEEDED = 30; // 30 frames for calibration (~1 second at 30fps)
const HISTORY_LENGTH = 10; // Keep last 10 detection results


  const WARNING_DELAY = 3000; // 3 seconds delay
  const INITIAL_DELAY = 10000; // 10 seconds initial delay
  const startTime = useRef(Date.now());

  const dialogStates = useSelector((state: any) => state?.dialog?.open);

  // Keep dialogStatesRef updated with the latest value
  useEffect(() => {
    dialogStatesRef.current = dialogStates;
  }, [dialogStates]);

  // Function to capture full screen screenshot
  const captureFullScreenshot = useCallback(async (): Promise<File | null> => {
    try {
      if (!screenShareRef.current) {
        console.warn("Screen share stream not available");
        return null;
      }

      const video = document.createElement("video");
      video.srcObject = screenShareRef.current;
      video.muted = true; // mute so play() can start without issues
      video.playsInline = true; // best practice for mobile Safari

      // Wait for metadata to load
      await new Promise<void>((resolve) => {
        if (video.readyState >= 1) resolve();
        else video.onloadedmetadata = () => resolve();
      });

      // Play video to get frames
      await video.play();

      // Wait a bit to ensure video frame is ready
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Setup canvas and draw the current video frame
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth || 1920;
      canvas.height = video.videoHeight || 1080;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("Could not get canvas context");
        return null;
      }

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Stop video and release stream
      video.pause();
      video.srcObject = null;

      // Convert canvas to Blob (PNG)
      const blob: Blob = await new Promise((resolve) =>
        canvas.toBlob((b) => resolve(b!), "image/png"),
      );

      // Convert Blob to File for upload or usage
      return new File([blob], "screenshot.png", { type: "image/png" });
    } catch (error) {
      console.error("Error capturing full screen screenshot:", error);
      return null;
    }
  }, []);

  const mergeMP4Blobs = useCallback(
    async (blobs: Uint8Array[]): Promise<Blob> => {
      // Prepare input files for ffmpeg
      blobs.forEach(async (blob, index) => {
        ffmpeg.FS("writeFile", `input${index}.mp4`, blob);
      });

      // Command to concatenate video files
      const fileInputs = blobs.map((_, index) => `input${index}.mp4`).join("|");
      await ffmpeg.run(
        "-i",
        `concat:${fileInputs}`,
        "-vcodec",
        "libx264",
        "-preset",
        "ultrafast", // use 'ultrafast' or 'faster' for faster encoding
        "-crf",
        "30", // higher CRF = more compression, lower quality (range: 18–35)
        "-pix_fmt",
        "yuv420p",
        "-movflags",
        "+faststart",
        "output.mp4",
      );
      // Read the output file from the FFmpeg file system
      const data = ffmpeg.FS("readFile", "output.mp4");

      // Create a new Blob from the output file data
      const finalBlob = [data.buffer] as any;
      const finalVideo = new Blob(finalBlob, { type: "video/mp4" });
      const formData = new FormData();
      formData.append(
        "file",
        finalVideo,
        `screening-interview-${interviewId}-${Date.now()}.mp4`,
      );
      answers.map(({ questionId, answer }) => {
        formData.append(`${questionId}`, answer);
      });
      const { success, ...response } = await interviewsApi.saveInterviewAnswers(
        interviewId,
        formData,
      );

      if (success) {
        setSubmitted(true);
        flashMessage(response.message, "success");
      } else {
        flashMessage(response.message, "error");
      }
      dispatch(setLoader(false));
      return finalVideo;
    },
    [answers, interviewId, dispatch],
  );

  const handleSubmit = useCallback(
    async (disqualified: boolean = false) => {
      // stop screenshare feature
      handleStopScreenShare();

      // Stop all exam functionalities immediately
      stopTimer();

      // Stop eye detection and clear all timeouts
      eyeDetection.current = false;
      if (eyeDetectionTimeoutRef.current) {
        clearTimeout(eyeDetectionTimeoutRef.current);
        eyeDetectionTimeoutRef.current = null;
      }
      if (eyeGazeDetectionTimeoutRef.current) {
        clearTimeout(eyeGazeDetectionTimeoutRef.current);
        eyeGazeDetectionTimeoutRef.current = null;
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
        warningTimeoutRef.current = null;
      }

      // Reset eye detection tracking
      eyeDetectionIssueStartTime.current = null;
      pendingEyeDetectionMessage.current = null;
      eyeGazeDetectionStartTime.current = null;
      pendingEyeGazeMessage.current = null;
      prevDetectionResult.current = "";
      isRequestInProgress.current = false;

      // Stop test immediately to prevent further detection loops
      startTest(false);

      // functionlity to save test
      if (mediaRecorderRef.current) {
        await dispatch(setLoader(true));
        await mediaRecorderRef.current.stop();
        setRecordingStopped(true);

        // upload generated video
        const finalVideo = new Blob(videoChunks, { type: "video/webm" });
        const formData = new FormData();
        formData.append(
          "file",
          finalVideo,
          `screening-interview-${interviewId}-${Date.now()}.mp4`,
        );
        answers.map(({ questionId, answer }) => {
          formData.append(`${questionId}`, answer);
        });

        if (disqualified) {
          formData.append(`disqualified`, "true");
        }
        const { success, ...response } =
          await interviewsApi.saveInterviewAnswers(interviewId, formData);

        if (success) {
          testDisqualified(disqualified);
          setSubmitted(true);
          flashMessage(response.message, "success");
        } else {
          flashMessage(response.message, "error");
        }
        dispatch(setLoader(false));
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [answers, dispatch, interviewId, videoChunks],
  );

  // Function to add a warning message max 5
  const addWarning = useCallback(
    async (message: string) => {
      const now = Date.now();

      // Prevent any warnings in the first 10 seconds or if exam is already submitted/disqualified
      if (
        now - startTime.current <= INITIAL_DELAY ||
        submitted ||
        isTestDisqualified
      ) {
        return;
      }
      if (interview?.show_warnings) {
        if (
          dialogStatesRef.current === false &&
          warningMessages.length < MAX_WARNINGS &&
          now - lastWarningTime.current >= WARNING_DELAY
        ) {
          // process to send the screenshot to backend
          await sendCapturedScreenshot(message);
          setWarningMessages((prev) => {
            const newWarnings = [...prev, message];

            if (newWarnings.length > MAX_WARNINGS) {
              newWarnings.shift(); // Remove the oldest warning
              handleSubmit(true);
            } else {
              const openWarningModal = () => {
                dispatch(
                  openDialog({
                    config: DialogComponents.INTERVIEW_WARNING_MESSAGE,
                    options: {
                      type: "WARNING",
                      message: message,
                      maxWarning: MAX_WARNINGS,
                      warningCount: newWarnings.length,
                    },
                  }),
                );
              };
              openWarningModal();
            }
            return newWarnings;
          });
          lastWarningTime.current = Date.now(); // Update the last warning time
          // flashMessage(message, "warning", 5); // Flash the warning message
        }
      } else {
        // if interview is schedule with show warnings false then don't show warnings and store in backend.
        await setWarningMessages((prev) => [...prev, message]);
        // process to send the screenshot to backend
        await sendCapturedScreenshot(message);
        lastWarningTime.current = Date.now();
      }
    },
    [
      lastWarningTime,
      warningMessages.length,
      handleSubmit,
      submitted,
      isTestDisqualified,
      interview?.show_warnings,
    ],
  );

  useEffect(() => {
    const processQueue = async () => {
      if (isProcessing || jobQueue.length === 0) return;

      setIsProcessing(true);
      const currentChunk = jobQueue[0]; // Get the first chunk from the queue

      try {
        // Load FFmpeg if not already loaded
        if (!ffmpeg.isLoaded()) {
          await ffmpeg.load();
        }
        const fileName = `recordings`;

        // Prepare the file for FFmpeg processing
        const fetchedChunk = await fetchFile(currentChunk);
        await ffmpeg.FS("writeFile", `${fileName}.webm`, fetchedChunk);
        // Run the FFmpeg command (adjust parameters as needed)
        await ffmpeg.run(
          "-i",
          `${fileName}.webm`,
          "-vcodec",
          "libx264",
          "-preset",
          "ultrafast",
          "-crf",
          "30",
          "-pix_fmt",
          "yuv420p",
          "-movflags",
          "+faststart",
          `${fileName}.mp4`,
        );

        // Read the processed file from FFmpeg's virtual filesystem
        const data = ffmpeg.FS("readFile", `${fileName}.mp4`);
        setUpdatedChunks((prevChunks) => [...prevChunks, data]);
        // Here you can save the processed file or do something else with it
      } catch (error) {
        console.error("Error processing video:", error);
      } finally {
        // Remove the processed chunk from the queue
        setJobQueue((prevQueue) => prevQueue.slice(1));
        setIsProcessing(false);
      }
    };

    processQueue();
  }, [jobQueue, isProcessing, updatedChunks.length]);

  useEffect(() => {
    if (
      recordingStopped &&
      updatedChunks.length > 0 &&
      (updatedChunks.length === videoChunks.length ||
        (!isProcessing && jobQueue.length == 0))
    ) {
      mergeMP4Blobs(updatedChunks);
    }
  }, [
    mergeMP4Blobs,
    recordingStopped,
    updatedChunks,
    videoChunks,
    isProcessing,
    jobQueue.length,
  ]);

  useEffect(() => {
    if (timeRemaining.time == 0) {
      handleSubmit();
    }
  }, [timeRemaining.time, handleSubmit]);

  useEffect(() => {
    const wasTestStarted = testStarted.current;
    testStarted.current = !submitted && isTestStart && !isTestDisqualified;

    // If test was running but now should stop, clean up immediately
    if (wasTestStarted && !testStarted.current) {
      eyeDetection.current = false;
      if (eyeDetectionTimeoutRef.current) {
        clearTimeout(eyeDetectionTimeoutRef.current);
        eyeDetectionTimeoutRef.current = null;
      }
      if (eyeGazeDetectionTimeoutRef.current) {
        clearTimeout(eyeGazeDetectionTimeoutRef.current);
        eyeGazeDetectionTimeoutRef.current = null;
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
        warningTimeoutRef.current = null;
      }
      eyeDetectionIssueStartTime.current = null;
      pendingEyeDetectionMessage.current = null;
      eyeGazeDetectionStartTime.current = null;
      pendingEyeGazeMessage.current = null;
      prevDetectionResult.current = "";
      isRequestInProgress.current = false;
    }
  }, [isTestStart, submitted, isTestDisqualified]);

  useEffect(() => {
    const initializeAndLoadModel = async () => {
      dispatch(setLoader(true));
      try {
        await tf.setBackend("webgl");
        await tf.ready();
      } catch (error) {
        console.error(
          "Error initializing backend, falling back to CPU:",
          error,
        );
        await tf.setBackend("cpu");
        await tf.ready();
      }
      try {
        const loadedModel = await blazeface.load();
        setModel(loadedModel);
      } catch (error) {
        console.error("Error loading the model:", error);
      }
      dispatch(setLoader(false));
    };
    initializeAndLoadModel();
  }, [dispatch]);

  // Eye detection logic
  // useEffect(() => {
  //   const detect = async () => {
  //     if (!model || !webcamRef.current) return;

  //     const video = webcamRef.current.video;
  //     if (!video || video.readyState !== 4) {
  //       // Retry after a short delay if video is not ready, but only if not submitted/disqualified
  //       if (testStarted.current && !submitted && !isTestDisqualified) {
  //         setTimeout(() => {
  //           if (testStarted.current && !submitted && !isTestDisqualified) {
  //             requestAnimationFrame(detect);
  //           }
  //         }, 100);
  //       }
  //       return;
  //     }

  //     try {
  //       // Use Blazeface to estimate face(s)
  //       const predictions = await model.estimateFaces(video, false);

  //       // Determine current face position (using the first prediction)
  //       let currentFacePosition = null;
  //       if (predictions.length > 0) {
  //         const face = predictions[0];
  //         // Using topLeft and bottomRight to compute center
  //         const [x, y] = face.topLeft as any;
  //         const [x2, y2] = face.bottomRight as any;
  //         currentFacePosition = {
  //           centerX: (x + x2) / 2,
  //           centerY: (y + y2) / 2,
  //         };
  //       }

  //       // Compare with previous face position if available - balanced tolerance
  //       let positionChanged = true;
  //       if (prevFacePosition.current && currentFacePosition) {
  //         const dx =
  //           currentFacePosition.centerX - prevFacePosition.current.centerX;
  //         const dy =
  //           currentFacePosition.centerY - prevFacePosition.current.centerY;
  //         const distance = Math.sqrt(dx * dx + dy * dy);
  //         // Reduced threshold from 25 to 15 pixels for more responsive detection
  //         if (distance < 10) {
  //           positionChanged = false;
  //         }
  //       }
  //       // Update previous face position
  //       prevFacePosition.current = currentFacePosition;

  //       // If no face is detected or the position hasn't changed, skip sending a request
  //       if (!currentFacePosition || !positionChanged) {
  //         // Continue detection loop regardless of round type, but only if not submitted/disqualified
  //         if (testStarted.current && !submitted && !isTestDisqualified) {
  //           requestAnimationFrame(detect);
  //         }
  //         return;
  //       }

  //       // Get the screenshot and send only if no request is already in progress
  //       const screenshot = webcamRef.current.getScreenshot();
  //       if (screenshot && !isRequestInProgress.current) {
  //         isRequestInProgress.current = true;
  //         try {
  //           if (dialogStates == false) {
  //             // Check if we're still in the initial delay period (10 seconds)
  //             const now = Date.now();
  //             if (now - startTime.current <= INITIAL_DELAY) {
  //               // Skip face detection during initial delay period
  //               isRequestInProgress.current = false;
  //               // Continue detection loop
  //               if (testStarted.current && !submitted && !isTestDisqualified) {
  //                 requestAnimationFrame(detect);
  //               }
  //               return;
  //             }

  //             // Convert the data URL to a Blob
  //             const blob = await fetch(screenshot).then((res) => res.blob());
  //             const formData = new FormData();
  //             formData.append("image", blob, "chunk.jpg");
  //             const { success, ...response } =
  //               await interviewsApi.faceDetection(formData);
  //             if (success && !response.data.valid) {
  //               // Check if this is an eye detection issue - match the exact backend messages
  //               const isEyeDetectionIssue =
  //                 response.message
  //                   .toLowerCase()
  //                   .includes("eyes were not clearly detected") ||
  //                 response.message.toLowerCase().includes("both eyes");

  //               // console.log("Is eye detection issue?", isEyeDetectionIssue);

  //               if (isEyeDetectionIssue) {
  //                 // Handle eye detection with 3-second delay
  //                 if (eyeDetectionIssueStartTime.current === null) {
  //                   // First time detecting eye issue - start the timer
  //                   eyeDetectionIssueStartTime.current = Date.now();
  //                   pendingEyeDetectionMessage.current = response.message; // Store the message
  //                   // console.log("Eye detection issue started, waiting 3 seconds...", response.message);

  //                   // Set timeout to show warning after 3 seconds
  //                   eyeDetectionTimeoutRef.current = setTimeout(() => {
  //                     // Check if the issue still persists after 3 seconds
  //                     if (
  //                       eyeDetectionIssueStartTime.current !== null &&
  //                       pendingEyeDetectionMessage.current
  //                     ) {
  //                       // console.log("Eye detection issue persisted for 3 seconds, showing warning");
  //                       addWarning(
  //                         `${new Date().toLocaleString()} \n ${pendingEyeDetectionMessage.current}`,
  //                       );
  //                       prevDetectionResult.current =
  //                         pendingEyeDetectionMessage.current;

  //                       // Reset the tracking after showing warning
  //                       eyeDetectionIssueStartTime.current = null;
  //                       pendingEyeDetectionMessage.current = null;
  //                     }
  //                   }, 3000); // 3 seconds delay
  //                 }
  //                 // If already tracking, don't do anything - let the timeout handle it
  //               } else {
  //                 // For non-eye detection issues, show warning immediately
  //                 if (prevDetectionResult.current !== response.message) {
  //                   addWarning(
  //                     `${new Date().toLocaleString()} \n ${response.message}`,
  //                   );
  //                   prevDetectionResult.current = response.message;
  //                   // Clear previous warning after 5 seconds
  //                   if (warningTimeoutRef.current) {
  //                     clearTimeout(warningTimeoutRef.current);
  //                   }
  //                   warningTimeoutRef.current = setTimeout(() => {
  //                     prevDetectionResult.current = "";
  //                     warningTimeoutRef.current = null;
  //                   }, 3000);
  //                 }
  //               }
  //             } else {
  //               // Reset all detection states when detection is OK
  //               prevDetectionResult.current = "";

  //               // Clear eye detection issue tracking
  //               if (eyeDetectionIssueStartTime.current !== null) {
  //                 // console.log("Eye detection issue resolved");
  //                 eyeDetectionIssueStartTime.current = null;
  //                 pendingEyeDetectionMessage.current = null;
  //                 if (eyeDetectionTimeoutRef.current) {
  //                   clearTimeout(eyeDetectionTimeoutRef.current);
  //                   eyeDetectionTimeoutRef.current = null;
  //                 }
  //               }
  //             }
  //           }
  //         } catch (error) {
  //           console.error("Error in face detection API call:", error);
  //         } finally {
  //           isRequestInProgress.current = false;
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error in face detection:", error);
  //     }

  //     // Continue detection loop if test is ongoing and not submitted/disqualified
  //     if (testStarted.current && !submitted && !isTestDisqualified) {
  //       requestAnimationFrame(detect);
  //     }
  //   };

  //   // Only start detection if test is active and not submitted/disqualified
  //   if (
  //     testStarted.current &&
  //     !eyeDetection.current &&
  //     !submitted &&
  //     !isTestDisqualified
  //   ) {
  //     detect();
  //     eyeDetection.current = true;
  //   }

  //   // Reset eye detection flag when test stops or is submitted/disqualified
  //   if (!testStarted.current || submitted || isTestDisqualified) {
  //     eyeDetection.current = false;

  //     // Clear eye detection timeout when test stops
  //     if (eyeDetectionTimeoutRef.current) {
  //       clearTimeout(eyeDetectionTimeoutRef.current);
  //       eyeDetectionTimeoutRef.current = null;
  //     }
  //     if (warningTimeoutRef.current) {
  //       clearTimeout(warningTimeoutRef.current);
  //       warningTimeoutRef.current = null;
  //     }
  //     eyeDetectionIssueStartTime.current = null;
  //     pendingEyeDetectionMessage.current = null;
  //     prevDetectionResult.current = "";
  //     isRequestInProgress.current = false;
  //   }
  // }, [model, isTestStart, submitted, isTestDisqualified, addWarning]);


  // Replace your existing detect function with this comprehensive version
useEffect(() => {
  const detect = async () => {
    if (!model || !webcamRef.current) return;

    const video = webcamRef.current.video;
    if (!video || video.readyState !== 4) {
      if (testStarted.current && !submitted && !isTestDisqualified) {
        setTimeout(() => {
          if (testStarted.current && !submitted && !isTestDisqualified) {
            requestAnimationFrame(detect);
          }
        }, 100);
      }
      return;
    }

    try {
      const predictions = await model.estimateFaces(video, false);
      const now = Date.now();

      // Skip detection during initial delay period
      if (now - startTime.current <= INITIAL_DELAY) {
        if (testStarted.current && !submitted && !isTestDisqualified) {
          requestAnimationFrame(detect);
        }
        return;
      }

      let hasValidFace = predictions.length > 0;
      let facePosition = null;
      let headMovement = 'normal';

      if (hasValidFace) {
        const face = predictions[0];
        
        // Calculate comprehensive face position
        facePosition = calculateFacePosition(face, video);
        
        // Calibrate baseline position if not done yet
        if (!isCalibrated.current) {
          calibrateBaselinePosition(facePosition);
        } else {
          // Detect head movement relative to baseline
          headMovement = detectHeadMovement(facePosition, baselineFacePosition.current);
        }
      } else {
        // No face detected - person might be looking completely away
        headMovement = 'away';
      }

      // Update detection history
      updateDetectionHistory(now, hasValidFace, facePosition);

      // Handle head movement state changes
      await handleHeadMovementStateChange(headMovement, now);

      // Additional check for face visibility consistency and frequent eye gaze detection
      if (shouldCallAPI(headMovement, now)) {
        await callFaceDetectionAPI(headMovement);
      }

      // More frequent API calls for eye gaze detection (every 3 seconds when face is detected)
      if (hasValidFace && now - lastApiCallTime.current >= 3000) {
        await callFaceDetectionAPI('eye_gaze_check');
      }

    } catch (error) {
      console.error("Error in face detection:", error);
    }

    // Continue detection loop with optimized frequency
    if (testStarted.current && !submitted && !isTestDisqualified) {
      setTimeout(() => {
        if (testStarted.current && !submitted && !isTestDisqualified) {
          requestAnimationFrame(detect);
        }
      }, 100); // Check every 100ms for smoother detection
    }
  };

  // Calculate comprehensive face position
  const calculateFacePosition = (face: any, video: HTMLVideoElement) => {
    const [x1, y1] = face.topLeft as [number, number];
    const [x2, y2] = face.bottomRight as [number, number];
    
    const videoWidth = video.videoWidth || 640;
    const videoHeight = video.videoHeight || 480;
    
    // Calculate face center and normalized position
    const centerX = (x1 + x2) / 2;
    const centerY = (y1 + y2) / 2;
    const faceWidth = x2 - x1;
    const faceHeight = y2 - y1;
    
    // Normalize positions (0-1 range)
    const normalizedX = centerX / videoWidth;
    const normalizedY = centerY / videoHeight;
    const normalizedWidth = faceWidth / videoWidth;
    const normalizedHeight = faceHeight / videoHeight;
    
    // Get landmarks if available
    let landmarks = null;
    if (face.landmarks && face.landmarks.length >= 6) {
      landmarks = face.landmarks.map((landmark: [number, number]) => [
        landmark[0] / videoWidth,  // Normalize X
        landmark[1] / videoHeight  // Normalize Y
      ]);
    }
    
    return {
      centerX: normalizedX,
      centerY: normalizedY,
      width: normalizedWidth,
      height: normalizedHeight,
      landmarks,
      rawBounds: { x1, y1, x2, y2 },
      videoSize: { width: videoWidth, height: videoHeight }
    };
  };

  // Calibrate baseline face position during first few frames
  const calibrateBaselinePosition = (facePosition: any) => {
    if (!facePosition) return;
    
    calibrationFrames.current++;
    
    if (!baselineFacePosition.current) {
      baselineFacePosition.current = { ...facePosition };
    } else {
      // Average the positions for more stable baseline
      const alpha = 0.1; // Smoothing factor
      baselineFacePosition.current.centerX = 
        baselineFacePosition.current.centerX * (1 - alpha) + facePosition.centerX * alpha;
      baselineFacePosition.current.centerY = 
        baselineFacePosition.current.centerY * (1 - alpha) + facePosition.centerY * alpha;
    }
    
    if (calibrationFrames.current >= CALIBRATION_FRAMES_NEEDED) {
      isCalibrated.current = true;
      console.log('Face detection calibrated with baseline:', baselineFacePosition.current);
    }
  };

  // Detect head movement relative to baseline position
  const detectHeadMovement = (currentPosition: any, baseline: any) => {
    if (!currentPosition || !baseline) return 'normal';
    
    const deltaX = currentPosition.centerX - baseline.centerX;
    const deltaY = currentPosition.centerY - baseline.centerY;
    const sizeRatio = currentPosition.width / baseline.width;
    
    // Thresholds for movement detection (adjustable based on testing)
    const horizontalThreshold = 0.15; // 15% of screen width
    const verticalThreshold = 0.12;   // 12% of screen height
    const sizeThreshold = 0.3;        // 30% size change
    
    // Check for specific movements
    if (Math.abs(deltaX) > horizontalThreshold) {
      if (deltaX > 0) {
        return 'right'; // Face moved right (person looking left)
      } else {
        return 'left';  // Face moved left (person looking right)
      }
    }
    
    if (Math.abs(deltaY) > verticalThreshold) {
      if (deltaY > 0) {
        return 'down';  // Face moved down (person looking down)
      } else {
        return 'up';    // Face moved up (person looking up)
      }
    }
    
    // Check if face became significantly smaller (person moved away)
    if (sizeRatio < (1 - sizeThreshold)) {
      return 'away';
    }
    
    // Additional landmark-based detection if available
    if (currentPosition.landmarks && baseline.landmarks) {
      const landmarkMovement = detectLandmarkMovement(currentPosition.landmarks, baseline.landmarks);
      if (landmarkMovement !== 'normal') {
        return landmarkMovement;
      }
    }
    
    return 'normal';
  };

  // Enhanced landmark-based movement detection
  const detectLandmarkMovement = (currentLandmarks: number[][], baselineLandmarks: number[][]) => {
    if (currentLandmarks.length < 6 || baselineLandmarks.length < 6) return 'normal';
    
    const [rightEye, leftEye, nose] = currentLandmarks;
    const [baseRightEye, baseLeftEye, baseNose] = baselineLandmarks;
    
    // Calculate eye-nose vectors for head pose
    const currentEyeCenter = [(rightEye[0] + leftEye[0]) / 2, (rightEye[1] + leftEye[1]) / 2];
    const baselineEyeCenter = [(baseRightEye[0] + baseLeftEye[0]) / 2, (baseRightEye[1] + baseLeftEye[1]) / 2];
    
    const noseEyeDeltaX = nose[0] - currentEyeCenter[0];
    const baseNoseEyeDeltaX = baseNose[0] - baselineEyeCenter[0];
    const noseEyeDeltaY = nose[1] - currentEyeCenter[1];
    const baseNoseEyeDeltaY = baseNose[1] - baselineEyeCenter[1];
    
    const horizontalDeviation = Math.abs(noseEyeDeltaX - baseNoseEyeDeltaX);
    const verticalDeviation = Math.abs(noseEyeDeltaY - baseNoseEyeDeltaY);
    
    // Thresholds for landmark-based detection
    const landmarkHorizontalThreshold = 0.05;
    const landmarkVerticalThreshold = 0.04;
    
    if (horizontalDeviation > landmarkHorizontalThreshold) {
      return noseEyeDeltaX > baseNoseEyeDeltaX ? 'right' : 'left';
    }
    
    if (verticalDeviation > landmarkVerticalThreshold) {
      return noseEyeDeltaY > baseNoseEyeDeltaY ? 'down' : 'up';
    }
    
    return 'normal';
  };

  // Update detection history for consistency checking
  const updateDetectionHistory = (timestamp: number, hasValidFace: boolean, position: any) => {
    faceDetectionHistory.current.push({
      timestamp,
      hasValidFace,
      position
    });
    
    // Keep only recent history
    if (faceDetectionHistory.current.length > HISTORY_LENGTH) {
      faceDetectionHistory.current.shift();
    }
  };

  // Handle head movement state changes with timers
  const handleHeadMovementStateChange = async (newMovement: string, now: number) => {
    const previousMovement = currentHeadState.current;
    
    if (newMovement !== previousMovement) {
      // Clear previous movement timer if exists
      if (headMovementTimeoutRefs.current[previousMovement]) {
        clearTimeout(headMovementTimeoutRefs.current[previousMovement]!);
        headMovementTimeoutRefs.current[previousMovement] = null;
        headMovementStartTime.current[previousMovement] = null;
      }
      
      // Update current state
      currentHeadState.current = newMovement;
      
      // Start new timer if movement is not normal
      if (newMovement !== 'normal') {
        headMovementStartTime.current[newMovement] = now;
        
        console.log(`Head movement detected: ${newMovement}, starting 5-second timer...`);
        
        headMovementTimeoutRefs.current[newMovement] = setTimeout(async () => {
          // Check if movement persists after delay
          if (currentHeadState.current === newMovement && 
              headMovementStartTime.current[newMovement]) {
            
            console.log(`Head ${newMovement} movement persisted for 5 seconds, calling API...`);
            
            if (now - lastApiCallTime.current >= API_CALL_COOLDOWN) {
              await callFaceDetectionAPI(newMovement);
              lastApiCallTime.current = Date.now();
            }
          }
        }, HEAD_MOVEMENT_DELAY);
      }
    }
  };

  // Determine if API should be called based on current state
  const shouldCallAPI = (movement: string, now: number) => {
    // Don't call API too frequently
    if (now - lastApiCallTime.current < API_CALL_COOLDOWN) {
      return false;
    }
    
    // Check for consistent problematic behavior in history
    const recentHistory = faceDetectionHistory.current.slice(-5); // Last 5 detections
    const consistentIssues = recentHistory.filter(h => !h.hasValidFace).length;
    
    // Call API if consistently losing face for multiple frames
    if (consistentIssues >= 3 && movement === 'away') {
      return true;
    }
    
    return false;
  };

  // Enhanced API call function
  const callFaceDetectionAPI = async (movementType: string) => {
    if (!webcamRef.current || isRequestInProgress.current || dialogStates) {
      return;
    }

    const screenshot = webcamRef.current.getScreenshot();
    if (!screenshot) return;

    isRequestInProgress.current = true;
    
    try {
      const blob = await fetch(screenshot).then((res) => res.blob());
      const formData = new FormData();
      formData.append("image", blob, "chunk.jpg");
      formData.append("movement_type", movementType); // Send movement type to backend
      
      const { success, ...response } = await interviewsApi.faceDetection(formData);
      
      if (success && !response.data.valid) {
        let message = `${new Date().toLocaleString()} \n`;
        
        // Customize message based on movement type
        switch (movementType) {
          case 'left':
            message += 'You are looking to the right. Please look directly at the camera.';
            break;
          case 'right':
            message += 'You are looking to the left. Please look directly at the camera.';
            break;
          case 'up':
            message += 'You are looking up. Please look directly at the camera.';
            break;
          case 'down':
            message += 'You are looking down. Please look directly at the camera.';
            break;
          case 'away':
            message += 'You are looking away from the camera. Please maintain eye contact with the camera.';
            break;
          default:
            message += response.message;
        }
        
        // Handle different types of detection issues with appropriate delays
        const isEyeDetectionIssue = response.message
          .toLowerCase()
          .includes("eyes were not clearly detected") ||
          response.message.toLowerCase().includes("both eyes");

        const isEyeGazeIssue = response.message
          .toLowerCase()
          .includes("eyes appear to be looking") ||
          response.message.toLowerCase().includes("eye pupil");

        if (isEyeDetectionIssue) {
          // Use existing eye detection delay logic (3 seconds)
          if (eyeDetectionIssueStartTime.current === null) {
            eyeDetectionIssueStartTime.current = Date.now();
            pendingEyeDetectionMessage.current = message;

            eyeDetectionTimeoutRef.current = setTimeout(() => {
              if (eyeDetectionIssueStartTime.current !== null &&
                  pendingEyeDetectionMessage.current) {
                addWarning(pendingEyeDetectionMessage.current);
                eyeDetectionIssueStartTime.current = null;
                pendingEyeDetectionMessage.current = null;
              }
            }, 3000);
          }
        } else if (isEyeGazeIssue) {
          // Handle eye gaze issues with 4-second delay
          if (eyeGazeDetectionStartTime.current === null) {
            eyeGazeDetectionStartTime.current = Date.now();
            pendingEyeGazeMessage.current = message;

            eyeGazeDetectionTimeoutRef.current = setTimeout(() => {
              if (eyeGazeDetectionStartTime.current !== null &&
                  pendingEyeGazeMessage.current) {
                addWarning(pendingEyeGazeMessage.current);
                eyeGazeDetectionStartTime.current = null;
                pendingEyeGazeMessage.current = null;
              }
            }, EYE_GAZE_DELAY); // 4 seconds for eye gaze issues
          }
        } else {
          // For head movement issues, show warning immediately after 5-second delay
          addWarning(message);
        }
      } else if (success && response.data.valid) {
        // Reset all states when detection is good
        resetAllDetectionStates();
      }
    } catch (error) {
      console.error("Error in face detection API call:", error);
    } finally {
      isRequestInProgress.current = false;
    }
  };

  // Reset all detection states
  const resetAllDetectionStates = () => {
    currentHeadState.current = 'normal';
    currentEyeGazeState.current = 'normal';
    prevDetectionResult.current = "";

    // Clear all head movement timers
    Object.keys(headMovementTimeoutRefs.current).forEach(key => {
      if (headMovementTimeoutRefs.current[key]) {
        clearTimeout(headMovementTimeoutRefs.current[key]!);
        headMovementTimeoutRefs.current[key] = null;
      }
      headMovementStartTime.current[key] = null;
    });

    // Clear all eye gaze timers
    Object.keys(eyeGazeTimeoutRefs.current).forEach(key => {
      if (eyeGazeTimeoutRefs.current[key]) {
        clearTimeout(eyeGazeTimeoutRefs.current[key]!);
        eyeGazeTimeoutRefs.current[key] = null;
      }
      eyeGazeStartTime.current[key] = null;
    });

    // Clear eye detection states
    if (eyeDetectionIssueStartTime.current !== null) {
      eyeDetectionIssueStartTime.current = null;
      pendingEyeDetectionMessage.current = null;
      if (eyeDetectionTimeoutRef.current) {
        clearTimeout(eyeDetectionTimeoutRef.current);
        eyeDetectionTimeoutRef.current = null;
      }
    }

    // Clear eye gaze detection states
    if (eyeGazeDetectionStartTime.current !== null) {
      eyeGazeDetectionStartTime.current = null;
      pendingEyeGazeMessage.current = null;
      if (eyeGazeDetectionTimeoutRef.current) {
        clearTimeout(eyeGazeDetectionTimeoutRef.current);
        eyeGazeDetectionTimeoutRef.current = null;
      }
    }
  };

  // Start detection
  if (testStarted.current && !eyeDetection.current && !submitted && !isTestDisqualified) {
    detect();
    eyeDetection.current = true;
  }

  // Cleanup when test stops
  if (!testStarted.current || submitted || isTestDisqualified) {
    eyeDetection.current = false;
    currentHeadState.current = 'normal';
    currentEyeGazeState.current = 'normal';
    isCalibrated.current = false;
    calibrationFrames.current = 0;
    baselineFacePosition.current = null;
    faceDetectionHistory.current = [];

    // Clear all head movement timers
    Object.keys(headMovementTimeoutRefs.current).forEach(key => {
      if (headMovementTimeoutRefs.current[key]) {
        clearTimeout(headMovementTimeoutRefs.current[key]!);
        headMovementTimeoutRefs.current[key] = null;
      }
      headMovementStartTime.current[key] = null;
    });

    // Clear all eye gaze timers
    Object.keys(eyeGazeTimeoutRefs.current).forEach(key => {
      if (eyeGazeTimeoutRefs.current[key]) {
        clearTimeout(eyeGazeTimeoutRefs.current[key]!);
        eyeGazeTimeoutRefs.current[key] = null;
      }
      eyeGazeStartTime.current[key] = null;
    });

    if (eyeDetectionTimeoutRef.current) {
      clearTimeout(eyeDetectionTimeoutRef.current);
      eyeDetectionTimeoutRef.current = null;
    }
    if (eyeGazeDetectionTimeoutRef.current) {
      clearTimeout(eyeGazeDetectionTimeoutRef.current);
      eyeGazeDetectionTimeoutRef.current = null;
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
      warningTimeoutRef.current = null;
    }

    eyeDetectionIssueStartTime.current = null;
    pendingEyeDetectionMessage.current = null;
    eyeGazeDetectionStartTime.current = null;
    pendingEyeGazeMessage.current = null;
    prevDetectionResult.current = "";
    isRequestInProgress.current = false;
  }
}, [model, isTestStart, submitted, isTestDisqualified, addWarning, dialogStates]);

  useEffect(() => {
    // Enhanced webcam reference monitoring for mode switching
    if (testStarted.current && webcamRef.current) {
      eyeDetection.current = false; // Force restart

      // Wait a bit for the webcam to be fully ready after mode switch
      const restartDetection = () => {
        if (testStarted.current && webcamRef.current?.video?.readyState === 4) {
          eyeDetection.current = false; // This will trigger detection restart in main effect
        } else if (testStarted.current) {
          // Retry if video is not ready yet
          setTimeout(restartDetection, 100);
        }
      };

      // Small delay to ensure webcam is ready
      setTimeout(restartDetection, 50);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showCodingRound]); // Monitor coding round changes instead of webcamRef.current

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isTestStart && !submitted && !isTestDisqualified) {
        addWarning(
          `${new Date().toLocaleString()}\n You have moved out from the current exam window. \n You are not allowed to move out from the exam page. \n if you continue to do like this your exam will be terminated.`,
        );
      }
    };

    // Only add listener if test is active and not submitted
    if (isTestStart && !submitted && !isTestDisqualified) {
      document.addEventListener("visibilitychange", handleVisibilityChange);
    }

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isTestStart, submitted, isTestDisqualified, addWarning]);

  useEffect(() => {
    const attemptedCount = answers.filter(
      (ans) => ans.answer && ans.answer.trim().length > 0,
    ).length;
    setAttemptQuestions(attemptedCount >= 0 ? attemptedCount : 0);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [answers]);

  useEffect(() => {
    const interviewQuestions: any = questions;
    if (interviewQuestions?.[currentQuestionIndex]?.type == 2) {
      setShowCodingRound(true);
      setCodingQuestionId(
        interviewQuestions?.[currentQuestionIndex]?.id as number,
      );
    } else {
      setShowCodingRound(false);
      setCodingQuestionId(null);
    }
  }, [currentQuestionIndex]);

  const detectHeadPose = (landmarks: [number, number][]) => {
    const nose = landmarks[2]; // Nose tip coordinates
    const leftEye = landmarks[0]; // Left eye coordinates
    const rightEye = landmarks[1]; // Right eye coordinates

    // Example head pose logic (simplified)
    const horizontalMidpoint = (leftEye[0] + rightEye[0]) / 2;

    // Check if the nose and chin are deviating too far from the midpoint
    const headTiltAngle = Math.abs(nose[0] - horizontalMidpoint);

    // console.log(headTiltAngle, "HEAD TILT ANGLE");

    // Reduced threshold from 50 to 40 for stricter head pose detection
    if (headTiltAngle > 10) {
      return true; // User might be looking away
    }

    return false;
  };

  const sendCapturedScreenshot = async (message: string) => {
    // Capture screenshot when show_warnings is false
    try {
      const screenshot = await captureFullScreenshot();
      if (screenshot) {
        const formData = new FormData();
        formData.append("warning_text", message);
        formData.append("image", screenshot);
        await interviewsApi.saveInterviewWarnings(interviewId, formData);
      }
    } catch (error) {
      console.error("Failed to capture screenshot:", error);
    }
  };

  // fetch querstion from backend
  const fetchAndSetQuestions = async () => {
    const { success, ...response } =
      await interviewsApi.candidateJobInterviewQuestions(interviewId);
    if (success) {
      const { questions } = response.data;
      setQuestions(questions);
    } else {
      router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
    }
  };

  // start timer to autosubmit
  const startTimer = () => {
    // Clear any previous timers if they exist
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Start a new timer
    timerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev.time <= 0) {
          clearInterval(timerRef.current); // Stop timer at 0
          return { ...prev, time: -1 };
        }

        const updatedTime = prev.time - 1;
        const percentage = (updatedTime / (testMintues * 60)) * 100; // Calculate percentage
        return {
          time: updatedTime,
          percentage,
        };
      });
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  // Convert time in seconds to "mm:ss" format
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${sec
      .toString()
      .padStart(2, "0")}`;
  };

  const handleMediaError = (error: any) => {
    let title = "",
      message = <></>;
    if (error.name === "NotAllowedError") {
      title = "Permissions denied!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          Please enable camera and microphone in your browser settings.
        </div>
      );
    } else if (error.name === "NotFoundError") {
      title = "No media devices found!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          Please check if your camera and microphone are connected. <br></br>
          Reload the page after connecting the devices.
        </div>
      );
    } else {
      title = "Could not access camera and microphone!";
      message = (
        <div className="mt-2 mb-2 pb-3">
          {error.message} <br></br>
          Reload the page after connecting the devices.
        </div>
      );
    }
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          closable: false,
          title: title,
          message: message,
        },
      }),
    );
  };

  // Start recording video chunks
  const startRecording = () => {
    // Check if the webcamRef and video element exist
    const mediaStream = webcamRef.current?.video?.srcObject as MediaStream;

    if (!stream) {
      console.error("No MediaStream available.");
      return;
    }

    // Initialize MediaRecorder with the webcam stream
    const mediaRecorder = new MediaRecorder(mediaStream, {
      mimeType: "video/webm",
    });

    mediaRecorderRef.current = mediaRecorder;

    mediaRecorder.ondataavailable = handleDataAvailable;

    // Start recording in chunks (1 second per chunk)
    startTimer(); // Stop the timer
    mediaRecorder.start(1000);
    startTest(true);

    // Reset the start time when test actually begins for accurate INITIAL_DELAY timing
    startTime.current = Date.now();
  };

  const handleStopScreenShare = () => {
    if (screenShareRef.current) {
      screenShareRef.current.getTracks().forEach((track) => track.stop());
      screenShareRef.current = null;
      setScreenShareStream(null);
    }
  };

  // Handle each recorded chunk
  const handleDataAvailable = async (event: BlobEvent) => {
    if (event.data.size > 0) {
      setVideoChunks((prevChunks) => [...prevChunks, event.data]);
    }
  };

  // event when test start
  const onStartTest = async () => {
    if (!screenShareStream) {
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: false,
        });

        // Check if the user selected "Entire Screen"
        const videoTrack = stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        // Chrome/Edge: displaySurface is 'monitor' for entire screen, 'window' for window, 'application' for app, 'browser' for tab
        // Firefox: may not support displaySurface, fallback to label check
        // @ts-ignore
        const displaySurface = settings?.displaySurface || videoTrack.label;

        if (
          displaySurface !== "monitor" &&
          !videoTrack.label.toLowerCase().includes("entire screen")
        ) {
          stream.getTracks().forEach((track) => track.stop());
          dispatch(
            openDialog({
              config: DialogComponents.CONFIRMATION_MODAL,
              options: {
                title: "Entire Screen Required",
                message: (
                  <div className="mt-2 mb-2">
                    <p>
                      You must select <b>Entire Screen</b> to start the exam.
                      <br />
                      Please try again and choose <b>Entire Screen</b> in the
                      screen share dialog.
                    </p>
                  </div>
                ),
              },
            }),
          );
          return;
        }

        setScreenShareStream(stream);
        screenShareRef.current = stream;
      } catch (err) {
        dispatch(
          openDialog({
            config: DialogComponents.CONFIRMATION_MODAL,
            options: {
              closable: false,
              title: "Screen Share Required",
              message: (
                <div>
                  <p>
                    You must allow screen sharing to start the exam.
                    <br />
                    Please refresh and allow screen share access.
                  </p>
                </div>
              ),
            },
          }),
        );
        return;
      }
    }
    await dispatch(setLoader(true));
    // write a function to fetch questions
    await fetchAndSetQuestions();
    await startRecording();
    // startTest(true);
    await dispatch(setLoader(false));
  };

  // This open the feedback form for the candidates
  const openFeedbackModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_INTERVIEW_FEEDBACK_MODAL,
        options: {
          interview: interview,
          onSubmitFeedback: () => {
            router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
          },
        },
      }),
    );
  };

  if (isTestDisqualified) {
    return (
      <DisqualificationNotice
        openFeedbackModal={openFeedbackModal}
        warningMessages={warningMessages}
      />
    );
  } else if (submitted) {
    return <SubmissionConfirmation openFeedbackModal={openFeedbackModal} />;
  }

  //

  const handleSetAnswer = (newAnswer: {
    questionId: number;
    answer: string;
  }) => {
    setAnswers((prevAnswers) => {
      const existingAnswerIndex = prevAnswers.findIndex(
        (answer) => answer.questionId === newAnswer.questionId,
      );
      if (existingAnswerIndex > -1) {
        // Update existing answer
        const updatedAnswers = [...prevAnswers];
        updatedAnswers[existingAnswerIndex] = newAnswer;
        return updatedAnswers;
      } else {
        // Add new answer
        return [...prevAnswers, newAnswer];
      }
    });
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prevIndex) => prevIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prevIndex) => prevIndex - 1);
    }
  };

  // const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleUserMedia = (stream: MediaStream) => {
    setStream(stream); // Save for later use

    // Ensure detection restarts when webcam is ready
    if (testStarted.current && model) {
      setTimeout(() => {
        if (testStarted.current && webcamRef.current?.video?.readyState === 4) {
          eyeDetection.current = false; // This will trigger detection restart
        }
      }, 100);
    }
  };

  const handleTrackScreen = () => {
    // Only handle screen tracking if exam is still active
    if (!submitted && !isTestDisqualified) {
      // Stop the exam and show dialog
      flashMessage(
        `You have stopped screen sharing.\n The exam has ended.`,
        "error",
      );
      handleSubmit(true);
    }
  };

  return (
    <>
      <div className="row">
        <div className={showCodingRound ? "" : "col-lg-6"}>
          <div
            className={`${showCodingRound ? "" : "video-screen card p-4 m-0 h-100"}`}>
            {isTestStart ? (
              <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                <h4 className="mb-2 heading-clr d-flex align-items-center">
                  AI Screening Round
                  {isTestStart && !submitted && (
                    <span className="record ms-auto">
                      Time Remaining: <b>{formatTime(timeRemaining.time)}</b>
                    </span>
                  )}
                </h4>
                <p className="mb-0 text-light-clr">
                  Start recording your video and answer the questions display on
                  right side with the the time limit.
                </p>
              </div>
            ) : (
              <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                <h4 className="mb-2 heading-clr d-flex align-items-center">
                  AI Screening Round
                </h4>
                <p className="mb-0 text-light-clr">
                  Start recording your video and answer the questions display on
                  right side with the the time limit.
                </p>
              </div>
            )}
            <div className="video-box">
              <div className="box">
                <WebcamWindow
                  audio={true}
                  videoConstraints={videoConstraints}
                  imageSmoothing={true}
                  disablePictureInPicture={true}
                  ref={webcamRef}
                  mirrored={true}
                  muted={true}
                  onUserMedia={handleUserMedia}
                  onUserMediaError={handleMediaError}
                  style={{
                    width: "100%",
                    height: "auto",
                    maxWidth: "1920px",
                    maxHeight: "1280px",
                  }}
                  mode={showCodingRound ? "coding" : "interview"}
                  screenStream={screenShareStream}
                  onScreenShareStopped={handleTrackScreen}
                />
                <div className="overlay"></div>
              </div>

              {!isTestStart && (
                <div className="start-box">
                  {/* {!isTestStart && !submitted && stream && ( */}
                  {!isTestStart && !submitted && (
                    <Button
                      className="btn btn-primary border-0 d-flex gap-2 align-items-center"
                      onClick={onStartTest}
                      disabled={isTestStart || !model}>
                      <Image
                        src="/images/play.svg"
                        alt="play"
                        width={11}
                        height={14}
                      />{" "}
                      Start
                    </Button>
                  )}
                  <div className="box">
                    <span className="round">
                      Round:{" "}
                      <b>
                        {interview.interview_round.toString().padStart(2, "0")}
                      </b>
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className={`col-lg-${showCodingRound ? "12" : "6"}`}>
          {isTestStart ? (
            showCodingRound ? (
              <CodingRound
                questionNumber={currentQuestionIndex + 1}
                currentQuestionIndex={currentQuestionIndex}
                questionId={codingQuestionId as number}
                questions={questions}
                answers={answers}
                onSetAnswer={handleSetAnswer}
                goToPreviousQuestion={goToPreviousQuestion}
                goToNextQuestion={goToNextQuestion}
                submitted={submitted}
                handleSubmit={handleSubmit}
              />
            ) : (
              <QuestionList
                questions={questions}
                answers={answers}
                setAnswers={setAnswers}
                handleSubmit={handleSubmit}
                submitted={submitted}
                interviewId={interviewId}
                stream={stream}
                currentQuestionIndex={currentQuestionIndex}
                attemptQuestions={attemptQuestions}
                handleSetAnswer={handleSetAnswer}
                goToPreviousQuestion={goToPreviousQuestion}
                goToNextQuestion={goToNextQuestion}
              />
            )
          ) : (
            <InterviewInstructions testMinutes={testMintues} />
          )}
        </div>
      </div>
    </>
  );
};
